import React, { useEffect, useRef } from 'react';
import '../styles/FuturisticTheme.css';

const VideographyHero = () => {
  const heroRef = useRef(null);
  
  useEffect(() => {
    // Add parallax effect on scroll
    const handleScroll = () => {
      if (!heroRef.current) return;
      
      const scrollPosition = window.scrollY;
      const parallaxElements = heroRef.current.querySelectorAll('.parallax');
      
      parallaxElements.forEach(element => {
        const speed = element.dataset.speed || 0.5;
        const yPos = -(scrollPosition * speed);
        element.style.transform = `translateY(${yPos}px)`;
      });
    };
    
    window.addEventListener('scroll', handleScroll);
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);
  
  return (
    <div 
      ref={heroRef}
      style={{
        position: 'relative',
        height: '70vh',
        minHeight: '500px',
        overflow: 'hidden',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: '2rem'
      }}
    >
      {/* Background video */}
      <div 
        className="parallax" 
        data-speed="0.3"
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          zIndex: 0
        }}
      >
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          backgroundImage: 'url(/images/sports/DSC06401-Enhanced-NR.jpg)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          filter: 'brightness(0.4)',
          transform: 'scale(1.1)'
        }}></div>
      </div>
      
      {/* Overlay gradient */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        background: 'linear-gradient(to bottom, rgba(10, 14, 23, 0.7), rgba(10, 14, 23, 0.9))',
        zIndex: 1
      }}></div>
      
      {/* Content */}
      <div style={{
        position: 'relative',
        zIndex: 2,
        textAlign: 'center',
        maxWidth: '800px',
        padding: '0 2rem'
      }}>
        <h1 
          className="animate-fadeInUp"
          style={{
            fontSize: 'clamp(2.5rem, 5vw, 4rem)',
            fontWeight: '800',
            color: 'white',
            marginBottom: '1.5rem',
            textTransform: 'uppercase',
            letterSpacing: '2px',
            textShadow: '0 2px 10px rgba(0, 0, 0, 0.5)'
          }}
        >
          Sports Videography
        </h1>
        
        <div 
          className="animate-fadeInUp"
          style={{
            width: '100px',
            height: '4px',
            background: 'var(--futuristic-gradient)',
            margin: '0 auto 2rem',
            borderRadius: '2px',
            boxShadow: 'var(--futuristic-glow)',
            animationDelay: '0.2s'
          }}
        ></div>
        
        <p 
          className="animate-fadeInUp"
          style={{
            fontSize: 'clamp(1.1rem, 2vw, 1.3rem)',
            lineHeight: '1.8',
            color: 'var(--futuristic-text-secondary)',
            marginBottom: '2.5rem',
            animationDelay: '0.3s'
          }}
        >
          Capturing the energy, emotion, and excitement of athletic competition through the lens.
          From dynamic highlights to in-depth documentaries, each video tells a compelling story.
        </p>
        
        <div 
          className="animate-fadeInUp"
          style={{
            display: 'flex',
            justifyContent: 'center',
            gap: '1rem',
            animationDelay: '0.4s'
          }}
        >
          <a 
            href="#featured-video-section"
            style={{
              display: 'inline-flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '0.8rem 2rem',
              background: 'var(--futuristic-gradient)',
              color: 'white',
              borderRadius: '50px',
              fontWeight: '600',
              textDecoration: 'none',
              boxShadow: 'var(--futuristic-glow)',
              transition: 'all 0.3s ease',
              gap: '0.5rem'
            }}
            onMouseOver={(e) => {
              e.currentTarget.style.transform = 'translateY(-3px)';
              e.currentTarget.style.boxShadow = '0 10px 25px rgba(0, 98, 255, 0.4)';
            }}
            onMouseOut={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = 'var(--futuristic-glow)';
            }}
            onClick={(e) => {
              e.preventDefault();
              document.getElementById('featured-video-section').scrollIntoView({ 
                behavior: 'smooth',
                block: 'start'
              });
            }}
          >
            <i className="fas fa-play-circle"></i>
            <span>Featured Videos</span>
          </a>
        </div>
        
        {/* Scroll indicator */}
        <div 
          className="animate-fadeInUp"
          style={{
            position: 'absolute',
            bottom: '-80px',
            left: '50%',
            transform: 'translateX(-50%)',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: '0.5rem',
            color: 'var(--futuristic-text-secondary)',
            fontSize: '0.9rem',
            opacity: 0.7,
            animationDelay: '0.5s'
          }}
        >
          <span>Scroll to explore</span>
          <i 
            className="fas fa-chevron-down animate-pulse" 
            style={{ fontSize: '1.2rem' }}
          ></i>
        </div>
      </div>
    </div>
  );
};

export default VideographyHero;
