.navigation {
  background-color: rgba(29, 53, 87, 0.95);
  padding: 0;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.19, 1, 0.22, 1);
  height: 80px;
}

.navigation.scrolled {
  background-color: rgba(14, 59, 125, 0.98);
  height: 70px;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
}

.nav-container {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 2rem;
}

.nav-brand {
  display: flex;
  align-items: center;
}

.brand-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  position: relative;
}

.brand-text {
  font-size: 1.8rem;
  font-weight: 800;
  color: white;
  letter-spacing: -1px;
  position: relative;
  z-index: 1;
  background: linear-gradient(135deg, #ffffff 0%, #f1faee 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brand-dot {
  width: 8px;
  height: 8px;
  background: linear-gradient(135deg, var(--secondary-color), var(--secondary-light));
  border-radius: 50%;
  margin-left: 2px;
  box-shadow: 0 0 10px rgba(230, 57, 70, 0.5);
  animation: pulse 2s infinite;
}

.nav-list {
  display: flex;
  justify-content: center;
  list-style: none;
  margin: 0;
  padding: 0;
  height: 100%;
}

.nav-item {
  margin: 0;
  position: relative;
  height: 100%;
  display: flex;
  align-items: center;
}

.nav-link {
  color: rgba(255, 255, 255, 0.85);
  text-decoration: none;
  font-size: 0.95rem;
  font-weight: 600;
  padding: 0 1.5rem;
  border: none;
  background: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.19, 1, 0.22, 1);
  text-transform: uppercase;
  letter-spacing: 1px;
  display: flex;
  align-items: center;
  position: relative;
  height: 100%;
  gap: 0.5rem;
}

.nav-icon {
  font-size: 1rem;
  transition: all 0.3s ease;
  opacity: 0.8;
}

.nav-link:hover {
  color: white;
}

.nav-link:hover .nav-icon {
  transform: translateY(-2px);
  opacity: 1;
  color: var(--secondary-color);
}

.nav-link.active {
  color: white;
}

.nav-link.active .nav-icon {
  color: var(--secondary-color);
  opacity: 1;
}

.nav-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 3px;
  background: linear-gradient(to right, var(--secondary-color), var(--secondary-light));
  border-radius: 3px;
  transition: all 0.3s cubic-bezier(0.19, 1, 0.22, 1);
  box-shadow: 0 2px 8px rgba(230, 57, 70, 0.3);
}

.nav-actions {
  display: flex;
  align-items: center;
}

.nav-action-button {
  background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
  color: white;
  border: none;
  padding: 0.7rem 1.5rem;
  border-radius: 50px;
  font-size: 0.9rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s cubic-bezier(0.19, 1, 0.22, 1);
  box-shadow: 0 4px 15px rgba(230, 57, 70, 0.3);
  text-decoration: none;
}

.nav-action-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(230, 57, 70, 0.4);
  background: linear-gradient(135deg, var(--secondary-light), var(--secondary-color));
  color: white;
}

.nav-action-button:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 10px rgba(230, 57, 70, 0.3);
}

.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: space-between;
  width: 30px;
  height: 21px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  z-index: 10;
  margin-left: 1.5rem;
}

.toggle-bar {
  width: 100%;
  height: 3px;
  background-color: white;
  border-radius: 3px;
  transition: all 0.3s cubic-bezier(0.19, 1, 0.22, 1);
}

@media (max-width: 1200px) {
  .nav-link {
    padding: 0 1rem;
    font-size: 0.9rem;
  }

  .nav-action-button {
    padding: 0.6rem 1.2rem;
    font-size: 0.85rem;
  }
}

@media (max-width: 992px) {
  .navigation {
    height: 70px;
  }

  .nav-list {
    position: fixed;
    top: 70px;
    left: 0;
    width: 100%;
    background-color: rgba(14, 59, 125, 0.98);
    flex-direction: column;
    height: auto;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s cubic-bezier(0.19, 1, 0.22, 1);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
  }

  .menu-open .nav-list {
    max-height: 500px;
  }

  .nav-item {
    width: 100%;
    height: auto;
  }

  .nav-link {
    padding: 1.2rem 2rem;
    width: 100%;
    justify-content: flex-start;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .nav-indicator {
    display: none;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  .menu-open .toggle-bar:nth-child(1) {
    transform: translateY(9px) rotate(45deg);
  }

  .menu-open .toggle-bar:nth-child(2) {
    opacity: 0;
  }

  .menu-open .toggle-bar:nth-child(3) {
    transform: translateY(-9px) rotate(-45deg);
  }
}

@media (max-width: 768px) {
  .nav-container {
    padding: 0 1.5rem;
  }

  .nav-actions {
    display: none;
  }

  .brand-text {
    font-size: 1.5rem;
  }

  .nav-link {
    padding: 1rem 1.5rem;
  }

  .nav-icon {
    font-size: 1.1rem;
    width: 25px;
  }
}
