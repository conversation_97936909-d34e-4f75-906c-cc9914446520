import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import './Navigation.css';

const Navigation = () => {
  const location = useLocation();
  const currentPath = location.pathname;
  const [scrolled, setScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Navigation sections with icons
  const sections = [
    { id: '/', label: 'Dashboard', icon: 'fa-home' },
    { id: '/broadcast', label: 'On-Air', icon: 'fa-microphone' },
    { id: '/photography', label: 'Photography', icon: 'fa-camera' },
    { id: '/videography', label: 'Videography', icon: 'fa-video' },
    { id: '/graphics', label: 'Graphic Design', icon: 'fa-palette' },
    { id: '/events', label: 'Events', icon: 'fa-calendar' },
    { id: '/contact', label: 'Contact', icon: 'fa-envelope' }
  ];

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close mobile menu when route changes
  useEffect(() => {
    setMobileMenuOpen(false);
  }, [location]);

  // Toggle mobile menu
  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <nav className={`navigation ${scrolled ? 'scrolled' : ''} ${mobileMenuOpen ? 'menu-open' : ''}`} id="main-nav">
      <div className="nav-container">
        <div className="nav-brand">
          <Link to="/" className="brand-link">
            <span className="brand-text">SM</span>
            <span className="brand-dot"></span>
          </Link>

          <button
            className="mobile-menu-toggle"
            onClick={toggleMobileMenu}
            aria-label="Toggle navigation menu"
          >
            <span className="toggle-bar"></span>
            <span className="toggle-bar"></span>
            <span className="toggle-bar"></span>
          </button>
        </div>

        <ul className="nav-list">
          {sections.map((section) => (
            <li key={section.id} className="nav-item">
              <Link
                to={section.id}
                className={`nav-link ${currentPath === section.id ? 'active' : ''}`}
                aria-label={`View ${section.label} section`}
              >
                <i className={`fas ${section.icon} nav-icon`}></i>
                <span className="nav-text">{section.label}</span>
                {currentPath === section.id && <span className="nav-indicator"></span>}
              </Link>
            </li>
          ))}
        </ul>

        <div className="nav-actions">
          <Link to="/contact" className="nav-action-button">
            <i className="fas fa-paper-plane"></i>
            <span>Get In Touch</span>
          </Link>
        </div>
      </div>
    </nav>
  );
};

export default Navigation;
