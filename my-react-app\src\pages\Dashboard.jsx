import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import '../components/Sections.css';
import './Dashboard.css';

const Dashboard = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [animatedCards, setAnimatedCards] = useState([]);
  const [heroActive, setHeroActive] = useState(false);
  // Removed mouse position state
  const sectionRef = useRef(null);
  const cardsRef = useRef(null);
  const heroRef = useRef(null);
  const statsRef = useRef(null);

  // Preload critical images for faster loading
  useEffect(() => {
    const criticalImages = [
      '/images/sports/750_0350-Enhanced-NR.jpg',
      '/images/sports/750_0441-Enhanced-NR.jpg',
      '/images/sports/DSC00061-Enhanced-NR.jpg',
      '/images/sports/DSC00113-Enhanced-NR.jpg',
      '/images/sports/DSC00156-Enhanced-NR.jpg'
    ];

    criticalImages.forEach(src => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = src;
      document.head.appendChild(link);
    });
  }, []);

  // Intersection Observer for animations
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1
    };

    const handleIntersection = (entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      });
    };

    const observer = new IntersectionObserver(handleIntersection, observerOptions);

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, []);

  // Activate hero section with delay
  useEffect(() => {
    const timer = setTimeout(() => {
      setHeroActive(true);
    }, 300);

    return () => clearTimeout(timer);
  }, []);

  // Removed all mouse tracking effects for professional static design

  // Animate cards on scroll with IntersectionObserver for better performance
  useEffect(() => {
    if (!cardsRef.current) return;

    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.15
    };

    const handleCardIntersection = (entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const index = parseInt(entry.target.dataset.index);
          setAnimatedCards(prev => {
            if (!prev.includes(index)) {
              return [...prev, index];
            }
            return prev;
          });
        }
      });
    };

    const cardObserver = new IntersectionObserver(handleCardIntersection, observerOptions);

    const cards = cardsRef.current.querySelectorAll('.dashboard-card');
    cards.forEach(card => {
      cardObserver.observe(card);
    });

    return () => {
      cards.forEach(card => {
        cardObserver.unobserve(card);
      });
    };
  }, []);

  // Removed parallax effect for more professional look
  const getParallaxStyle = () => {
    return {}; // Return empty object to prevent movement
  };

  // Portfolio sections with enhanced descriptions and better images
  const sections = [
    {
      id: 'broadcast',
      title: 'On-Air Experience',
      description: 'Professional sports broadcasting showcasing my versatility as an on-air talent, including play-by-play commentary, sideline reporting, and in-depth studio analysis.',
      imageUrl: '/images/sports/750_0350-Enhanced-NR.jpg',
      icon: 'fas fa-microphone-alt',
      badge: 'Featured',
      stats: '50+ Live Events'
    },
    {
      id: 'photography',
      title: 'Sports Photography',
      description: 'Capturing the decisive moments in sports through a professional lens, from the intensity of action shots to the emotion and atmosphere that defines athletic competition.',
      imageUrl: '/images/sports/750_0441-Enhanced-NR.jpg',
      icon: 'fas fa-camera',
      badge: 'Award Winning',
      stats: '1000+ Photos'
    },
    {
      id: 'videography',
      title: 'Sports Videography',
      description: 'Cinematic sports storytelling through video, including highlight reels, documentary-style features, and promotional content that brings the excitement of sports to life.',
      imageUrl: '/images/sports/DSC00061-Enhanced-NR.jpg',
      icon: 'fas fa-video',
      badge: 'Professional',
      stats: '25+ Productions'
    },
    {
      id: 'graphics',
      title: 'Graphic Design',
      description: 'Creative visual solutions for sports brands and events, including team branding, digital assets, motion graphics, and comprehensive marketing campaign materials.',
      imageUrl: '/images/sports/DSC00113-Enhanced-NR.jpg',
      icon: 'fas fa-palette',
      badge: 'Creative',
      stats: '100+ Designs'
    },
    {
      id: 'events',
      title: 'Event Coverage',
      description: 'Comprehensive sports event coverage combining broadcasting, photography, and videography to deliver immersive experiences for audiences across multiple platforms.',
      imageUrl: '/images/sports/DSC00113-Enhanced-NR.jpg',
      icon: 'fas fa-calendar-alt',
      badge: 'Exclusive',
      stats: '30+ Major Events'
    }
  ];

  // Additional showcase images for the gallery
  const showcaseImages = [
    '/images/sports/DSC00231-Enhanced-NR.jpg',
    '/images/sports/DSC00732-Enhanced-NR.jpg',
    '/images/sports/DSC04917-Enhanced-NR.jpg',
    '/images/sports/DSC05591-Enhanced-NR.jpg',
    '/images/sports/DSC05711-Enhanced-NR.jpg',
    '/images/sports/DSC06401-Enhanced-NR.jpg',
    '/images/sports/DSC06411-Enhanced-NR.jpg',
    '/images/sports/DSC07025.jpg',
    '/images/sports/DSC07308-Enhanced-NR.jpg',
    '/images/sports/DSC07394-Enhanced-NR.jpg',
    '/images/sports/DSC08306-Enhanced-NR.jpg',
    '/images/sports/DSC08558-Enhanced-NR.jpg',
    '/images/sports/DSC08928.jpg',
    '/images/sports/DSC09229-Enhanced-NR.jpg',
    '/images/sports/DSC09584-Enhanced-NR.jpg',
    '/images/sports/DSC09840-Enhanced-NR.jpg',
    '/images/sports/DSC_1913.jpg',
    '/images/sports/DSC_4909-Enhanced-NR.jpg',
    '/images/sports/DSC_5152-Enhanced-NR.jpg',
    '/images/sports/DSC_5338-Enhanced-NR.jpg',
    '/images/sports/DSC_5376.jpg',
    '/images/sports/DSC_5608-Enhanced-NR.jpg',
    '/images/sports/DSC_5713.jpg'
  ];

  // Statistics for the dashboard
  const statistics = [
    { value: '5+', label: 'Years Experience', icon: 'fas fa-clock' },
    { value: '100+', label: 'Sports Events', icon: 'fas fa-trophy' },
    { value: '25+', label: 'Major Clients', icon: 'fas fa-handshake' },
    { value: '1000+', label: 'Media Assets', icon: 'fas fa-photo-video' }
  ];

  // Smooth scroll to content
  const scrollToContent = () => {
    const contentElement = document.getElementById('portfolio-sections');
    if (contentElement) {
      contentElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  return (
    <section className="section dashboard-section" id="dashboard" ref={sectionRef}>
      {/* Hero Section with Professional Design */}
      <div className={`dashboard-hero ${heroActive ? 'active' : ''}`} ref={heroRef}>
        <div className="dashboard-hero-bg"></div>
        <div className="dashboard-hero-overlay"></div>
        <div className="dashboard-hero-content">
          <div className="hero-badge">Sports Media Professional</div>
          <h1 className="dashboard-hero-title">Siddartha Manubothu</h1>
          <p className="dashboard-hero-subtitle">
            Capturing the essence of sports through Broadcasting, Photography, Videography, and Graphic Design
          </p>
          <div className="hero-actions">
            <a href="#portfolio-sections" className="dashboard-hero-cta primary" onClick={(e) => {
              e.preventDefault();
              scrollToContent();
            }}>
              Explore My Work <i className="fas fa-arrow-down"></i>
            </a>
            <Link to="/contact" className="dashboard-hero-cta secondary">
              Get In Touch <i className="fas fa-paper-plane"></i>
            </Link>
          </div>

          <div className="hero-scroll-indicator">
            <div className="scroll-icon">
              <div className="scroll-wheel"></div>
            </div>
            <span>Scroll to explore</span>
          </div>
        </div>
      </div>

      {/* Statistics Section */}
      <div className={`dashboard-stats ${isVisible ? 'visible' : ''}`} ref={statsRef}>
        {statistics.map((stat, index) => (
          <div className="stat-item" key={index} style={{ animationDelay: `${0.1 * index}s` }}>
            <div className="stat-icon">
              <i className={stat.icon}></i>
            </div>
            <div className="stat-value">{stat.value}</div>
            <div className="stat-label">{stat.label}</div>
          </div>
        ))}
      </div>

      {/* Portfolio Sections */}
      <div className="sections-title" id="portfolio-sections">
        <h2>My Portfolio</h2>
        <div className="title-accent"></div>
        <p>
          Explore my comprehensive sports media portfolio showcasing professional work across multiple disciplines.
          Each section represents a unique aspect of my expertise in the sports media landscape.
        </p>
      </div>

      <div className={`dashboard-grid ${isVisible ? 'visible' : ''}`} ref={cardsRef}>
        {sections.map((section, index) => (
          <Link
            to={`/${section.id}`}
            key={section.id}
            className={`dashboard-card ${animatedCards.includes(index) ? 'animated' : ''}`}
            style={{ animationDelay: `${0.1 * index}s` }}
            data-index={index}
          >
            <div className="dashboard-card-inner">
              <div
                className="dashboard-card-image"
                style={{ backgroundImage: `url(${section.imageUrl})` }}
              >
                <div className="dashboard-card-overlay">
                  <i className={`${section.icon} dashboard-card-icon`}></i>
                </div>
                {section.badge && (
                  <div className="dashboard-card-badge">{section.badge}</div>
                )}
              </div>
              <div className="dashboard-card-content">
                <h3 className="dashboard-card-title">{section.title}</h3>
                <p className="dashboard-card-description">{section.description}</p>
                <div className="dashboard-card-stats">
                  <i className="fas fa-chart-line"></i>
                  <span>{section.stats}</span>
                </div>
                <div className="dashboard-card-button">
                  <span>Explore</span>
                  <i className="fas fa-arrow-right"></i>
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>

      {/* Featured Work Section */}
      <div className={`dashboard-featured ${isVisible ? 'visible' : ''}`}>
        <div className="featured-header">
          <h3 className="dashboard-featured-title">Featured Work</h3>
          <div className="featured-badge">Latest Project</div>
        </div>
        <div className="dashboard-featured-content">
          <div className="dashboard-featured-image">
            <img src="/images/sports/DSC00156-Enhanced-NR.jpg" alt="Featured sports work" />
            <div className="featured-overlay">
              <div className="play-button">
                <i className="fas fa-play"></i>
              </div>
            </div>
          </div>
          <div className="dashboard-featured-text">
            <div className="featured-meta">
              <span className="featured-date"><i className="far fa-calendar"></i> June 2023</span>
              <span className="featured-category"><i className="fas fa-tag"></i> Championship Series</span>
            </div>
            <h4>Championship Coverage Highlights</h4>
            <p>
              My comprehensive coverage of the recent championship series showcases the full range of my sports media capabilities.
              This project combined live broadcasting, dynamic photography, cinematic videography, and custom graphic design
              to deliver a complete media package that captured every aspect of this prestigious sporting event.
            </p>
            <div className="featured-tags">
              <span className="featured-tag">Broadcasting</span>
              <span className="featured-tag">Photography</span>
              <span className="featured-tag">Videography</span>
              <span className="featured-tag">Design</span>
            </div>
            <Link to="/broadcast" className="dashboard-featured-button">
              <span>View Featured Work</span>
              <i className="fas fa-arrow-right"></i>
            </Link>
          </div>
        </div>
      </div>

      {/* Sports Showcase Gallery */}
      <div className={`dashboard-showcase ${isVisible ? 'visible' : ''}`}>
        <div className="showcase-header">
          <h3 className="showcase-title">Sports Portfolio Showcase</h3>
          <p className="showcase-description">
            A glimpse into my comprehensive sports media work across various sporting events and competitions
          </p>
        </div>
        <div className="showcase-gallery">
          {showcaseImages.map((image, index) => (
            <div
              key={index}
              className="showcase-item"
              style={{ animationDelay: `${0.02 * index}s` }}
            >
              <img
                src={image}
                alt={`Sports portfolio ${index + 1}`}
                loading="lazy"
                decoding="async"
              />
              <div className="showcase-overlay">
                <i className="fas fa-search-plus"></i>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Call to Action */}
      <div className={`dashboard-cta ${isVisible ? 'visible' : ''}`}>
        <div className="cta-content">
          <h3>Ready to collaborate?</h3>
          <p>Let's create exceptional sports media content together</p>
          <Link to="/contact" className="cta-button">
            Get In Touch <i className="fas fa-arrow-right"></i>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default Dashboard;
