.dashboard-section {
  background-color: var(--background-color);
  position: relative;
  padding: 0;
  overflow: hidden;
}

.dashboard-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(14, 59, 125, 0.03), transparent 70%),
              radial-gradient(circle at bottom left, rgba(230, 57, 70, 0.03), transparent 70%);
  z-index: -1;
}

/* Professional Hero Section */
.dashboard-hero {
  position: relative;
  height: 100vh;
  min-height: 700px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0;
  overflow: hidden;
  background-color: #000;
}

.dashboard-hero-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('/images/sports/750_0350-Enhanced-NR.jpg');
  background-size: cover;
  background-position: center;
  filter: brightness(0.7) saturate(1.2) contrast(1.1);
  transition: all 1.2s cubic-bezier(0.19, 1, 0.22, 1);
  opacity: 0;
  transform: scale(1.05);
  will-change: opacity;
}

.dashboard-hero.active .dashboard-hero-bg {
  opacity: 1;
  transform: scale(1);
}

.dashboard-hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.85)),
              linear-gradient(135deg, rgba(14, 59, 125, 0.7), transparent 80%);
  z-index: 1;
}

.dashboard-hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 900px;
  padding: 0 2rem;
  color: white;
  margin-top: -30px;
}

.hero-badge {
  display: inline-block;
  background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
  color: white;
  padding: 0.6rem 1.5rem;
  border-radius: 4px;
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 2rem;
  box-shadow: 0 5px 15px rgba(230, 57, 70, 0.2);
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.8s forwards 0.1s;
}

.dashboard-hero-title {
  font-size: 4.5rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  line-height: 1.1;
  text-shadow: 0 3px 10px rgba(0, 0, 0, 0.4);
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.8s forwards 0.3s;
  color: #ffffff;
  letter-spacing: -0.5px;
}

.dashboard-hero-subtitle {
  font-size: 1.4rem;
  font-weight: 400;
  margin-bottom: 3rem;
  line-height: 1.6;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.8s forwards 0.5s;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.hero-actions {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  margin-bottom: 4rem;
  flex-wrap: wrap;
}

.dashboard-hero-cta {
  display: inline-flex;
  align-items: center;
  gap: 0.8rem;
  padding: 0.9rem 2rem;
  border-radius: 4px;
  font-weight: 600;
  font-size: 1rem;
  text-decoration: none;
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateY(20px);
  will-change: transform, box-shadow;
}

.dashboard-hero-cta.primary {
  background: var(--primary-color);
  color: white;
  box-shadow: 0 4px 12px rgba(14, 59, 125, 0.3);
  animation: fadeInUp 0.8s forwards 0.7s;
}

.dashboard-hero-cta.secondary {
  background: transparent;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  animation: fadeInUp 0.8s forwards 0.8s;
}

.dashboard-hero-cta.primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(14, 59, 125, 0.4);
  background: var(--dark-color);
}

.dashboard-hero-cta.secondary:hover {
  transform: translateY(-3px);
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

.dashboard-hero-cta i {
  transition: transform 0.3s ease;
}

.dashboard-hero-cta:hover i {
  transform: translateX(5px);
}

.hero-scroll-indicator {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.85rem;
  opacity: 0;
  animation: fadeIn 0.8s forwards 1s;
}

.scroll-icon {
  width: 26px;
  height: 42px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  margin-bottom: 8px;
  position: relative;
}

.scroll-wheel {
  position: absolute;
  top: 6px;
  left: 50%;
  width: 4px;
  height: 4px;
  background: white;
  border-radius: 50%;
  transform: translateX(-50%);
  animation: scrollWheel 1.5s infinite;
}

@keyframes scrollWheel {
  0% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) translateY(15px);
  }
}

/* Statistics Section */
.dashboard-stats {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  max-width: 1200px;
  margin: 0 auto;
  padding: 4rem 2rem;
  gap: 2rem;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s cubic-bezier(0.19, 1, 0.22, 1);
}

.dashboard-stats.visible {
  opacity: 1;
  transform: translateY(0);
}

.stat-item {
  flex: 1;
  min-width: 200px;
  background: white;
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1);
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.6s forwards;
  animation-play-state: paused;
  border-bottom: 4px solid var(--primary-color);
}

.dashboard-stats.visible .stat-item {
  animation-play-state: running;
}

.stat-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
}

.stat-icon {
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  color: white;
  font-size: 1.8rem;
  box-shadow: 0 10px 20px rgba(14, 59, 125, 0.2);
}

.stat-value {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
  line-height: 1;
}

.stat-label {
  font-size: 1rem;
  color: #666;
  font-weight: 500;
}

/* Portfolio Sections Title */
.sections-title {
  text-align: center;
  margin: 5rem auto 4rem;
  padding: 0 2rem;
  max-width: 900px;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 1s forwards 0.3s;
}

.sections-title h2 {
  font-size: 3rem;
  font-weight: 800;
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  position: relative;
  display: inline-block;
  letter-spacing: -0.5px;
}

.title-accent {
  width: 80px;
  height: 4px;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  margin: 0 auto 2rem;
  border-radius: 2px;
}

.sections-title p {
  font-size: 1.3rem;
  color: #555;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.7;
}

/* Portfolio Grid */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2.5rem;
  margin: 4rem auto;
  padding: 0 2rem;
  max-width: 1400px;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s cubic-bezier(0.19, 1, 0.22, 1);
}

.dashboard-grid.visible {
  opacity: 1;
  transform: translateY(0);
}

.dashboard-card {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: all 0.4s ease;
  height: 100%;
  text-decoration: none;
  color: var(--text-color);
  background-color: #fff;
  position: relative;
  transform: translateY(30px);
  opacity: 0;
  animation: fadeInUp 0.6s ease forwards;
  animation-play-state: paused;
  will-change: transform, box-shadow;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.dashboard-grid.visible .dashboard-card.animated {
  animation-play-state: running;
}

.dashboard-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
  z-index: 1;
}

.dashboard-card-inner {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.dashboard-card-image {
  height: 250px;
  background-size: cover;
  background-position: center;
  position: relative;
  overflow: hidden;
}

.dashboard-card-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.6));
  z-index: 1;
  transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
}

.dashboard-card:hover .dashboard-card-image::before {
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.7));
}

.dashboard-card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
  z-index: 2;
}

.dashboard-card-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  background: var(--secondary-color);
  color: white;
  padding: 0.4rem 0.8rem;
  border-radius: 3px;
  font-size: 0.75rem;
  font-weight: 600;
  z-index: 3;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.dashboard-card:hover .dashboard-card-badge {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.dashboard-card-icon {
  font-size: 3rem;
  color: #fff;
  opacity: 0.9;
  transition: all 0.4s ease;
  text-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
  transform: translateY(5px);
}

.dashboard-card:hover .dashboard-card-icon {
  transform: translateY(0);
  opacity: 1;
}

.dashboard-card-content {
  padding: 1.75rem;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  background: white;
  position: relative;
  z-index: 2;
}

.dashboard-card-title {
  font-size: 1.5rem;
  margin-bottom: 0.8rem;
  color: var(--primary-color);
  font-weight: 700;
  transition: all 0.3s ease;
}

.dashboard-card:hover .dashboard-card-title {
  color: var(--dark-color);
}

.dashboard-card-description {
  font-size: 0.95rem;
  color: #555;
  margin-bottom: 1.5rem;
  flex-grow: 1;
  line-height: 1.6;
}

.dashboard-card-stats {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  color: var(--accent-color);
  font-weight: 600;
  font-size: 0.85rem;
}

.dashboard-card-stats i {
  font-size: 0.85rem;
}

.dashboard-card-button {
  display: inline-flex;
  align-items: center;
  font-weight: 600;
  color: var(--secondary-color);
  transition: all 0.3s ease;
  margin-top: auto;
  font-size: 0.9rem;
}

.dashboard-card-button i {
  margin-left: 0.5rem;
  transition: transform 0.3s ease;
}

.dashboard-card:hover .dashboard-card-button {
  color: var(--secondary-dark);
}

.dashboard-card:hover .dashboard-card-button i {
  transform: translateX(5px);
}

/* Featured Work Section */
.dashboard-featured {
  max-width: 1400px;
  margin: 6rem auto;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
  position: relative;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.dashboard-featured.visible {
  opacity: 1;
  transform: translateY(0);
}

.featured-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background: var(--primary-color);
  position: relative;
}

.dashboard-featured-title {
  margin: 0;
  color: #fff;
  font-size: 1.6rem;
  font-weight: 600;
  position: relative;
  z-index: 1;
}

.featured-badge {
  background: var(--secondary-color);
  color: white;
  padding: 0.4rem 0.8rem;
  border-radius: 3px;
  font-size: 0.75rem;
  font-weight: 600;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
}

.dashboard-featured-content {
  display: flex;
  flex-direction: column;
}

.dashboard-featured-image {
  flex: 1;
  min-height: 400px;
  overflow: hidden;
  position: relative;
}

.dashboard-featured-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.8s cubic-bezier(0.19, 1, 0.22, 1);
  will-change: transform;
}

.dashboard-featured:hover .dashboard-featured-image img {
  transform: scale(1.05);
}

.featured-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.6));
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.dashboard-featured:hover .featured-overlay {
  opacity: 1;
}

.play-button {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.8rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  transform: scale(0.9);
}

.dashboard-featured:hover .play-button {
  transform: scale(1);
}

.play-button:hover {
  transform: scale(1.1);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
}

.dashboard-featured-text {
  flex: 1;
  padding: 2.5rem;
}

.featured-meta {
  display: flex;
  gap: 2rem;
  margin-bottom: 1.2rem;
  color: #777;
  font-size: 0.85rem;
}

.featured-meta i {
  margin-right: 0.5rem;
  color: var(--primary-color);
}

.dashboard-featured-text h4 {
  font-size: 1.8rem;
  margin-bottom: 1.2rem;
  color: var(--primary-color);
  font-weight: 600;
  line-height: 1.3;
}

.dashboard-featured-text p {
  margin-bottom: 1.5rem;
  color: #555;
  font-size: 1rem;
  line-height: 1.6;
}

.featured-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.6rem;
  margin-bottom: 1.5rem;
}

.featured-tag {
  background-color: var(--gray-light);
  color: var(--primary-color);
  padding: 0.4rem 0.8rem;
  border-radius: 3px;
  font-size: 0.8rem;
  font-weight: 500;
}

.dashboard-featured-button {
  display: inline-flex;
  align-items: center;
  padding: 0.8rem 1.5rem;
  background: var(--primary-color);
  color: #fff;
  border-radius: 4px;
  font-weight: 600;
  font-size: 0.9rem;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(14, 59, 125, 0.2);
}

.dashboard-featured-button i {
  margin-left: 0.6rem;
  transition: transform 0.3s ease;
}

.dashboard-featured-button:hover {
  background: var(--dark-color);
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(14, 59, 125, 0.3);
}

.dashboard-featured-button:hover i {
  transform: translateX(5px);
}

/* Call to Action Section */
.dashboard-cta {
  max-width: 1200px;
  margin: 6rem auto;
  padding: 0 2rem;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s cubic-bezier(0.19, 1, 0.22, 1);
}

.dashboard-cta.visible {
  opacity: 1;
  transform: translateY(0);
}

.cta-content {
  background: var(--primary-color);
  padding: 3rem;
  border-radius: 8px;
  text-align: center;
  color: white;
  box-shadow: 0 15px 40px rgba(14, 59, 125, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.cta-content h3 {
  font-size: 2rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.cta-content p {
  font-size: 1.1rem;
  margin-bottom: 1.8rem;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-button {
  display: inline-flex;
  align-items: center;
  padding: 0.8rem 1.8rem;
  background: white;
  color: var(--primary-color);
  border-radius: 4px;
  font-weight: 600;
  font-size: 1rem;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.cta-button i {
  margin-left: 0.6rem;
  transition: transform 0.3s ease;
}

.cta-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  background: #f8f9fa;
}

.cta-button:hover i {
  transform: translateX(5px);
}

/* Responsive Styles */
@media (min-width: 992px) {
  .dashboard-featured-content {
    flex-direction: row;
  }

  .dashboard-featured-image {
    flex: 0 0 50%;
  }
}

@media (max-width: 1200px) {
  .dashboard-hero-title {
    font-size: 4rem;
  }

  .dashboard-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (max-width: 992px) {
  .dashboard-hero-title {
    font-size: 3.5rem;
  }

  .dashboard-hero-subtitle {
    font-size: 1.3rem;
  }

  .dashboard-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }

  .cta-content {
    padding: 3rem 2rem;
  }

  .cta-content h3 {
    font-size: 2.2rem;
  }
}

@media (max-width: 768px) {
  .dashboard-hero-title {
    font-size: 3rem;
  }

  .dashboard-hero-subtitle {
    font-size: 1.1rem;
  }

  .hero-actions {
    flex-direction: column;
    gap: 1rem;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-stats {
    justify-content: center;
  }

  .stat-item {
    min-width: 150px;
    flex: 0 0 calc(50% - 1rem);
  }

  .featured-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .dashboard-featured-text {
    padding: 2rem;
  }

  .dashboard-featured-text h4 {
    font-size: 1.8rem;
  }

  .cta-content h3 {
    font-size: 1.8rem;
  }

  .cta-content p {
    font-size: 1rem;
  }
}

@media (max-width: 576px) {
  .dashboard-hero-title {
    font-size: 2.5rem;
  }

  .dashboard-hero-subtitle {
    font-size: 1rem;
  }

  .dashboard-hero-cta {
    width: 100%;
    justify-content: center;
  }

  .stat-item {
    flex: 0 0 100%;
  }

  .sections-title h2 {
    font-size: 2.2rem;
  }

  .sections-title p {
    font-size: 1.1rem;
  }
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
